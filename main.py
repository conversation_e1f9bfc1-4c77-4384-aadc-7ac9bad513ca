
import streamlit as st
import asyncio
import nest_asyncio
from dotenv import load_dotenv

from agent.utils import run_search_queries
from agent.formatter import format_search_query_results
from agent.schema import SearchQuery

# Safe async support for Streamlit
nest_asyncio.apply()
load_dotenv()  # Load TAVILY_API_KEY from .env

# --- Page Configuration ---
st.set_page_config(page_title="AI Research Agent", page_icon="🔎", layout="wide")

# --- Custom CSS for Styling ---
st.markdown("""
    <style>
        html, body, [class*="css"]  {
            font-family: 'Segoe UI', sans-serif;
            background-color: #f8f9fa;
        }
        .main-title {
            font-size: 3rem;
            font-weight: 800;
            color: #1e3a8a;
            margin-bottom: 0.2em;
        }
        .subtext {
            font-size: 1.2rem;
            color: #555;
            margin-bottom: 1em;
        }
        .stTextArea textarea {
            background-color: #ffffff;
            font-size: 0.95rem;
            border-radius: 0.5rem;
        }
        .stButton>button {
            background-color: #1e3a8a;
            color: white;
            border-radius: 8px;
            font-weight: 600;
            padding: 0.6em 2em;
            font-size: 1rem;
        }
        .stButton>button:hover {
            background-color: #374fc7;
            transition: 0.2s ease-in-out;
        }
    </style>
""", unsafe_allow_html=True)

# --- Header ---
st.markdown('<div class="main-title">🔎 AI Research Agent</div>', unsafe_allow_html=True)
st.markdown('<div class="subtext">Your intelligent assistant for streamlined web research.</div>', unsafe_allow_html=True)

# --- Input Section ---
with st.container():
    st.markdown("### 📝 Enter Your Search Queries")
    query_input = st.text_area(
        "One query per line:",
        height=100,
        placeholder="e.g.\nImpact of climate change on agriculture\nLatest trends in artificial intelligence"
    )

# --- Settings Section ---
with st.expander("Advanced Settings", expanded=True):
    col1, col2 = st.columns(2)
    with col1:
        include_raw = st.checkbox("Include raw webpage content?", value=True)
    with col2:
        max_tokens = st.slider("Max tokens for raw content (truncation):", 100, 3000, 500)

# Warn if token value seems extreme
if max_tokens < 150:
    st.warning("⚠️ Too few tokens may cut off important content.")
elif max_tokens > 2000:
    st.info("High token count may slow down performance slightly.")

# --- Run Button ---
st.markdown("---")
run_button = st.button("🚀 Run Search", use_container_width=True)

# --- Search Execution ---
async def perform_search(queries, include_raw, max_tokens):
    docs = await run_search_queries(queries, include_raw_content=include_raw)
    if docs:
        return format_search_query_results(docs, max_tokens=max_tokens, include_raw_content=include_raw)
    return None

if run_button:
    if not query_input.strip():
        st.warning("🚨 Please enter at least one search query.")
    else:
        queries = [
            SearchQuery(search_query=q.strip())
            for q in query_input.strip().splitlines()
            if q.strip()
        ]

        with st.spinner("🔍 Searching the web for you..."):
            formatted = asyncio.run(perform_search(queries, include_raw, max_tokens))

            if formatted:
                st.success("✅ Search completed!")
                st.markdown("### 📄 Formatted Research Results")
                st.code(formatted, language="markdown")
            else:
                st.error("❌ No documents found. Try adjusting your queries.")












# import os
# from dotenv import load_dotenv
# load_dotenv()  # Load from .env file
# from agent.utils import run_search_queries
# from agent.formatter import format_search_query_results

# import asyncio

# async def main():
#     queries = ['what is machine learning']
#     docs = await run_search_queries(queries, include_raw_content=True)

#     if docs:
#         formatted = format_search_query_results(docs, max_tokens=500, include_raw_content=True)
#         print(formatted)
#     else:
#         print("No documents returned from search.")

# if __name__ == "__main__":
#     asyncio.run(main())

# streamlit_app.py





# import streamlit as st
# import asyncio
# from agent.utils import run_search_queries
# from agent.formatter import format_search_query_results
# from agent.schema import SearchQuery
# from dotenv import load_dotenv

# load_dotenv()  # Load TAVILY_API_KEY from .env

# st.set_page_config(page_title="AI Research Agent", layout="wide")

# st.title("🔎 Web Research Assistant")

# query_input = st.text_area("Enter your search queries (one per line):", height=200)
# include_raw = st.checkbox("Include raw webpage content?", value=True)
# max_tokens = st.slider("Max tokens for raw content (truncation):", 100, 3000, 500)

# if st.button("Run Search"):
#     if not query_input.strip():
#         st.warning("Please enter at least one query.")
#     else:
#         # Split queries by line
#         queries = [SearchQuery(search_query=q.strip()) for q in query_input.strip().splitlines() if q.strip()]

#         async def main():
#             with st.spinner("Running searches..."):
#                 docs = await run_search_queries(queries, include_raw_content=include_raw)
#                 if docs:
#                     formatted = format_search_query_results(docs, max_tokens=max_tokens, include_raw_content=include_raw)
#                     st.success("Search completed!")
#                     st.text_area("Formatted Output:", formatted, height=500)
#                 else:
#                     st.error("No documents returned from search.")

#         asyncio.run(main())

