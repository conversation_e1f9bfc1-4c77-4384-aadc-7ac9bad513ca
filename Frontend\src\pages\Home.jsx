import { useState } from 'react';
import { Search, FileText, Plus, ChevronLeft, ChevronRight, RefreshCw, Home, Star, 
  X, MoreHorizontal, BookOpen, Boxes, Bookmark, ThermometerSun, CornerUpRight, 
  MessageSquare, Download, Globe, ArrowRight, Cloud, ExternalLink, Menu } from 'lucide-react';

export default function PerplexityUI() {
  const [searchQuery, setSearchQuery] = useState('');
  const [sidebarOpen, setSidebarOpen] = useState(true);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <div className="flex flex-col h-screen bg-gray-100">
      {/* Browser chrome */}
     

      {/* Main content */}
      <div className="flex flex-1 overflow-hidden bg-white">
        {/* Sidebar */}
        <div className={`${sidebarOpen ? 'w-40' : 'w-0'} bg-white border-r border-gray-200 flex flex-col gap-4 transition-all duration-300 overflow-hidden`}>
          <div className="flex items-center p-3">
            {/* <img src="/api/placeholder/24/24" alt="Perplexity Logo" className="h-6" /> */}
            <span className="text-blue-600 font-semibold text-sm ml-7 mt-1">Ai Research</span>
          </div>

          <div className="flex items-center text-xs gap-2 border border-gray-200 rounded p-1 px-2 mx-3">
            <span className="text-gray-700">New Thread</span>
            <span className="text-gray-400 text-xs">Ctrl+I</span>
          </div>

          <nav className="flex flex-col gap-3 text-xs px-3">
            <div className="flex items-center gap-2 text-gray-700 hover:bg-gray-100 p-1 rounded cursor-pointer">
              <Home size={14} />
              <span>Home</span>
            </div>
            <div className="flex items-center gap-2 text-gray-700 hover:bg-gray-100 p-1 rounded cursor-pointer">
              <Globe size={14} />
              <span>Discover</span>
            </div>
            <div className="flex items-center gap-2 text-gray-700 hover:bg-gray-100 p-1 rounded cursor-pointer">
              <Boxes size={14} />
              <span>Spaces</span>
            </div>
            <div className="flex items-center gap-2 text-gray-700 hover:bg-gray-100 p-1 rounded cursor-pointer">
              <BookOpen size={14} />
              <span>Library</span>
            </div>
          </nav>

          <div className="mt-auto p-3">
            <div className="flex items-center gap-2 text-xs text-gray-700">
              <div className="bg-gray-200 rounded-full w-5 h-5 flex items-center justify-center text-xs">
                S
              </div>
              <span>shubhamua...</span>
            </div>
            <div className="mt-2 flex items-center justify-between text-xs">
              <span className="text-gray-700">Download</span>
              <span className="text-red-500 bg-red-100 rounded-full px-1 text-xs">1</span>
            </div>
          </div>
        </div>

        {/* Toggle sidebar button */}
        <button 
          onClick={toggleSidebar} 
          className="absolute top-4 left-2 z-10 p-1 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-700"
        >
          <Menu size={16} />
        </button>

        {/* Main content area */}
        <div className="flex-1 flex flex-col items-center pt-16 pl-6 overflow-y-auto">
          <div className={`w-full max-w-xl px-4 transition-all duration-300 ${sidebarOpen ? 'ml-0' : 'ml-0'}`}>
            <h1 className="text-2xl font-medium text-center mb-6">What do you want to know?</h1>
            
            {/* Search input */}
            <div className="relative mb-6">
              <input
                type="text"
                className="w-full border border-gray-200 rounded-lg p-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-400"
                placeholder="Ask anything..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              
              {/* Search options */}
              <div className="flex mt-2 gap-2 items-center">
                <div className="flex items-center gap-1 px-2 py-1 rounded-md bg-gray-100 text-xs">
                  <Search size={12} className="text-gray-500" />
                  <span>Search</span>
                </div>
                <div className="flex items-center gap-1 px-2 py-1 rounded-md bg-gray-100 text-xs">
                  <FileText size={12} className="text-gray-500" />
                  <span>Research</span>
                </div>
                <div className="flex-1"></div>
                <div className="flex items-center gap-1">
                  <div className="w-1.5 h-1.5 rounded-full bg-gray-300"></div>
                  <div className="w-1.5 h-1.5 rounded-full bg-gray-300"></div>
                  <div className="w-1.5 h-1.5 rounded-full bg-gray-300"></div>
                  <button className="bg-blue-500 text-white rounded-full p-1">
                    <ArrowRight size={14} />
                  </button>
                </div>
              </div>
            </div>
            
            {/* Card section */}
            <div className="grid grid-cols-2 gap-3 mb-8">
              <div className="bg-blue-50 p-3 rounded-md border border-blue-100">
                <div className="text-xs text-gray-700 mb-1">Introducing our Windows App</div>
                <div className="text-xs text-gray-500">Install the native Windows App</div>
              </div>
              
              <div className="bg-gray-50 p-3 rounded-md border border-gray-100 flex flex-col">
                <div className="flex items-center gap-1 mb-1">
                  <ThermometerSun size={14} className="text-gray-600" />
                  <span className="text-base font-medium">32°C</span>
                </div>
                <div className="text-xs text-gray-600">Pune</div>
                <div className="text-xs text-gray-400 mt-auto">H: 35°L: 28°</div>
              </div>
              
              <div className="bg-gray-50 p-3 rounded-md border border-gray-100 flex">
                <img src="/api/placeholder/32/32" alt="News Thumbnail" className="w-8 h-8 mr-2 rounded" />
                <div>
                  <div className="text-xs text-gray-700">Supreme Court may block parents' right to...</div>
                </div>
              </div>
              
              <div className="bg-gray-50 p-3 rounded-md border border-gray-100">
                <div className="text-xs font-medium mb-1">Finance</div>
                <div className="text-xs text-gray-500">Get your watchlist</div>
              </div>
            </div>
          </div>
          
          {/* Footer */}
          <div className="w-full border-t border-gray-200 mt-auto">
            <div className="max-w-xl mx-auto px-4 py-2 flex text-xs text-gray-500 gap-3">
              <span className="hover:text-gray-700">Pro</span>
              <span className="hover:text-gray-700">Enterprise</span>
              <span className="hover:text-gray-700">API</span>
              <span className="hover:text-gray-700">Blog</span>
              <span className="hover:text-gray-700">Careers</span>
              <span className="hover:text-gray-700">Store</span>
              <span className="hover:text-gray-700">Finance</span>
              <div className="flex items-center hover:text-gray-700">
                <span>English</span>
                <ChevronRight size={12} className="ml-1" />
              </div>
            </div>
          </div>
        </div>
      </div>
      
      
    </div>
  );
}