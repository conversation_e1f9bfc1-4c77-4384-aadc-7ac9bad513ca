# 🔎 AI Research Agent - Frontend

A modern, responsive React frontend for the AI Research Agent application. Built with React 19, Vite, and Tailwind CSS, this interface provides an intuitive way to interact with AI-powered research capabilities.

## ✨ Features

- **Modern React Architecture**: Built with React 19 and functional components
- **Lightning Fast Development**: Powered by Vite for instant hot module replacement
- **Beautiful UI**: Styled with Tailwind CSS for responsive, modern design
- **Icon Library**: Lucide React icons for consistent, beautiful iconography
- **Component-Based**: Modular architecture with reusable components
- **Research Interface**: Specialized UI components for AI research interactions

## 🚀 Quick Start

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn package manager

### Installation

1. **Clone the repository** (if not already done):

   ```bash
   git clone <repository-url>
   cd AI-Research-Agent/Frontend
   ```

2. **Install dependencies**:

   ```bash
   npm install
   ```

3. **Start the development server**:

   ```bash
   npm run dev
   ```

4. **Open your browser** and navigate to `http://localhost:5173`

## 📁 Project Structure

```
Frontend/
├── public/                 # Static assets
├── src/
│   ├── components/        # Reusable UI components
│   │   ├── PerplexityUI.jsx
│   │   └── page.jsx
│   ├── pages/            # Page components
│   │   ├── Home.jsx
│   │   ├── Login.jsx
│   │   └── profile.jsx
│   ├── assets/           # Images, icons, etc.
│   ├── App.jsx           # Main application component
│   ├── App.css           # Application styles
│   ├── main.jsx          # Application entry point
│   └── index.css         # Global styles
├── index.html            # HTML template
├── package.json          # Dependencies and scripts
├── vite.config.js        # Vite configuration
├── tailwind.config.js    # Tailwind CSS configuration
└── eslint.config.js      # ESLint configuration
```

## 🛠️ Available Scripts

| Command           | Description                              |
| ----------------- | ---------------------------------------- |
| `npm run dev`     | Start development server with hot reload |
| `npm run build`   | Build production-ready application       |
| `npm run preview` | Preview production build locally         |
| `npm run lint`    | Run ESLint for code quality checks       |

## 🎨 Tech Stack

- **React 19** - Latest React with improved performance and features
- **Vite** - Next-generation frontend build tool
- **Tailwind CSS** - Utility-first CSS framework
- **Lucide React** - Beautiful, customizable icons
- **ESLint** - Code linting and quality assurance

## 🔧 Configuration

### Tailwind CSS

The project uses Tailwind CSS v4 with Vite integration. Configuration can be found in `tailwind.config.js`.

### Vite

Vite configuration is in `vite.config.js` with React plugin enabled for fast refresh.

### ESLint

ESLint is configured with React-specific rules in `eslint.config.js` for code quality.

## 🌟 Key Components

### PerplexityUI

A specialized component for AI research interactions, providing an intuitive interface for research queries.

### Page Components

- **Home**: Main landing page with research interface
- **Login**: User authentication interface
- **Profile**: User profile management

## 🚀 Development Workflow

1. **Start Development**: Run `npm run dev` to start the development server
2. **Make Changes**: Edit components in the `src/` directory
3. **Hot Reload**: Changes are automatically reflected in the browser
4. **Lint Code**: Run `npm run lint` to check code quality
5. **Build**: Run `npm run build` when ready for production

## 📱 Responsive Design

The application is fully responsive and works seamlessly across:

- Desktop computers
- Tablets
- Mobile devices

## 🔗 Integration

This frontend is designed to work with the AI Research Agent Python backend. Make sure the backend server is running for full functionality.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is part of the AI Research Agent application. Please refer to the main project license.

## 🆘 Support

If you encounter any issues or have questions:

1. Check the [Issues](../../issues) section
2. Review the documentation
3. Contact the development team

---

**Happy Researching! 🔎✨**
