import Image from "next/image"
import { Search, ChevronDown, Menu, Info, BookmarkIcon, Settings, Thermometer, Sun } from "lucide-react"
import { Button } from "@/components/ui/button"
import Link from "next/link"

export default function Home() {
  return (
    <div className="flex h-screen flex-col">
      {/* Header */}
      <header className="border-b border-gray-200 bg-white">
        <div className="flex items-center justify-between px-4 py-2">
          <div className="flex items-center space-x-2">
            <button className="rounded-full p-1 hover:bg-gray-100">
              <Menu className="h-5 w-5 text-gray-500" />
            </button>
            <button className="rounded-full p-1 hover:bg-gray-100">
              <ChevronDown className="h-5 w-5 text-gray-500" />
            </button>
          </div>
          <div className="flex items-center space-x-2">
            <button className="rounded-full p-1 hover:bg-gray-100">
              <BookmarkIcon className="h-5 w-5 text-gray-500" />
            </button>
            <button className="rounded-full p-1 hover:bg-gray-100">
              <Info className="h-5 w-5 text-gray-500" />
            </button>
            <button className="rounded-full p-1 hover:bg-gray-100">
              <Settings className="h-5 w-5 text-gray-500" />
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex flex-1">
        {/* Sidebar */}
        <div className="w-[115px] border-r border-gray-200 bg-gray-50">
          <div className="p-4">
            <Image
              src="/placeholder.svg?height=40&width=90"
              alt="Perplexity Logo"
              width={90}
              height={40}
              className="mb-6"
            />
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>New Thread</span>
              <span>Ctrl + T</span>
            </div>
          </div>
          <nav className="mt-4 space-y-1 px-2">
            {["Home", "Discover", "Spaces", "Library"].map((item) => (
              <Link
                key={item}
                href="#"
                className="flex items-center rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200"
              >
                {item}
              </Link>
            ))}
          </nav>
          <div className="absolute bottom-4 left-4 flex items-center space-x-2">
            <div className="h-8 w-8 rounded-full bg-gray-300"></div>
            <span className="text-xs text-gray-500">User</span>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 bg-white">
          <div className="mx-auto max-w-3xl px-4 py-12">
            <h1 className="mb-8 text-center text-3xl font-semibold text-gray-800">What do you want to know?</h1>
            <div className="relative mb-8">
              <input
                type="text"
                placeholder="Ask anything..."
                className="w-full rounded-lg border border-gray-300 px-4 py-3 focus:border-blue-500 focus:outline-none"
              />
              <div className="mt-2 flex items-center space-x-2">
                <Button className="flex items-center space-x-1 rounded-md bg-gray-100 px-3 py-1 text-sm text-gray-700 hover:bg-gray-200">
                  <Search className="h-4 w-4" />
                  <span>Search</span>
                </Button>
                <Button variant="outline" className="flex items-center space-x-1 rounded-md px-3 py-1 text-sm">
                  <span>Research</span>
                </Button>
                <div className="ml-auto flex items-center space-x-2">
                  <div className="h-5 w-5 rounded-full bg-gray-200"></div>
                  <div className="h-5 w-5 rounded-full bg-gray-200"></div>
                  <div className="h-5 w-5 rounded-full bg-gray-200"></div>
                  <div className="flex h-7 w-7 items-center justify-center rounded-full bg-teal-500 text-white">
                    <span>+</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Cards Section */}
            <div className="grid grid-cols-2 gap-4">
              <div className="rounded-lg border border-gray-200 p-4">
                <div className="mb-2 text-sm font-medium">Introducing our Windows App</div>
                <div className="text-xs text-gray-500">Install the native Windows App</div>
                <div className="mt-2 flex justify-end">
                  <Button className="rounded-md bg-blue-500 px-2 py-1 text-xs text-white">Get</Button>
                </div>
              </div>

              <div className="rounded-lg border border-gray-200 p-4">
                <div className="mb-2 flex items-center space-x-2">
                  <Thermometer className="h-4 w-4" />
                  <span className="font-medium">32°C</span>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-xs">Sunny</div>
                    <div className="text-xs text-gray-500">Pune</div>
                  </div>
                  <Sun className="h-5 w-5 text-yellow-500" />
                </div>
              </div>

              <div className="rounded-lg border border-gray-200 p-4">
                <div className="mb-2 text-sm font-medium">Supreme Court may block parents' right to...</div>
                <div className="flex items-center justify-between">
                  <div className="h-8 w-8 rounded bg-blue-100"></div>
                </div>
              </div>

              <div className="rounded-lg border border-gray-200 p-4">
                <div className="mb-2 text-sm font-medium">Finance</div>
                <div className="text-xs text-gray-500">Get your watchlist</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="border-t border-gray-200 bg-white p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <Link href="#" className="hover:text-gray-900">
              Pro
            </Link>
            <Link href="#" className="hover:text-gray-900">
              Enterprise
            </Link>
            <Link href="#" className="hover:text-gray-900">
              API
            </Link>
            <Link href="#" className="hover:text-gray-900">
              Blog
            </Link>
            <Link href="#" className="hover:text-gray-900">
              Careers
            </Link>
            <Link href="#" className="hover:text-gray-900">
              Store
            </Link>
            <Link href="#" className="hover:text-gray-900">
              Finance
            </Link>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">English</span>
            <ChevronDown className="h-4 w-4 text-gray-500" />
          </div>
        </div>
      </footer>
    </div>
  )
}
